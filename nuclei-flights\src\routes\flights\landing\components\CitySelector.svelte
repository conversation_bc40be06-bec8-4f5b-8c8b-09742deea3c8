<script lang="ts">
  // Import Svelte's event dispatcher to communicate with parent components
  import { createEventDispatcher } from "svelte";

  export let selectedCities;
  export let isModalOpen = false; // New prop to track modal state

  // Initialize dispatcher for custom events
  const dispatch = createEventDispatcher();

  // Dispatches a 'click' event with the type ("From" or "To")
  function handleClick(type: any) {
    dispatch("click", type);
  }

  // Dispatches a 'swap' event to parent to swap the departure and destination cities
  function swapCities(event: any) {
    event.stopPropagation();
    dispatch("swap");
  }
</script>

<!-- Flight Input Cards -->
<div class="relative">
  <!-- From Section -->
  <div
    class="cursor-pointer w-full flex items-center justify-between p-4 mb-3 bg-white border border-gray-200 rounded-lg shadow-sm text-left px-4 relative"
    on:click={() => handleClick("From")}
  >
    <div class="flex items-center">
      <div class="flex-shrink-0 mr-3">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="25"
          viewBox="0 0 24 25"
          fill="none"
        >
          <path
            d="M2 22.5H22"
            stroke="#8A8A8A"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M6.36 17.8991L4 17.4991L2 13.4991L3.1 12.9491C3.37916 12.8085 3.6874 12.7352 4 12.7352C4.3126 12.7352 4.62084 12.8085 4.9 12.9491L5.07 13.0491C5.34916 13.1898 5.6574 13.2631 5.97 13.2631C6.2826 13.2631 6.59084 13.1898 6.87 13.0491L8 12.4991L5 6.49915L5.9 6.04915C6.23267 5.88549 6.60429 5.81753 6.97335 5.85285C7.34242 5.88816 7.6944 6.02537 7.99 6.24915L12.01 9.24915C12.3066 9.47486 12.6604 9.61319 13.0315 9.64853C13.4025 9.68387 13.7761 9.61481 14.11 9.44915L18.3 7.38915C18.8354 7.11924 19.4523 7.05862 20.03 7.21915L21 7.49915C21.2004 7.5548 21.3859 7.65442 21.543 7.79075C21.7001 7.92708 21.8248 8.09671 21.9082 8.28729C21.9915 8.47787 22.0313 8.68463 22.0247 8.89252C22.0181 9.10042 21.9652 9.30423 21.87 9.48915L21.49 10.2491C21.26 10.7091 20.89 11.0891 20.42 11.3291L7.58 17.6991C7.20245 17.8862 6.77547 17.9492 6.36 17.8791V17.8991Z"
            stroke="#8A8A8A"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="flex-1">
        <div class="select-none text-gray-500 pl-3 text-xs">From</div>
        <div class="flex items-baseline">
          <span class="select-none text-gray-900 pl-3 font-bold text-lg"
            >{selectedCities.from.city}</span
          >
          <span class="select-none pl-2 text-sm text-gray-600"
            >{selectedCities.from.code}</span
          >
        </div>
        <div class="select-none text-gray-500 pl-3 text-xs">
          {selectedCities.from.airport}
        </div>
      </div>
    </div>

    <!-- Swap Cities Button -->
    <div
      class="absolute -bottom-8 right-4"
      class:z-10={!isModalOpen}
      class:z-0={isModalOpen}
    >
      <div
        class="cursor-pointer p-1.5 bg-white border-2 border-blue-500 rounded-full shadow-sm hover:bg-gray-50"
        on:click={swapCities}
      >
        <svg
          width="24"
          height="24"
          viewBox="0 0 36 36"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="w-6 h-6"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M18.4956 15.954C18.353 16.0966 18.353 16.3211 18.4956 16.4637C18.6334 16.6014 18.8627 16.6014 19.0002 16.4637L21.5847 13.8792V22.4076C21.5847 22.6064 21.7427 22.7696 21.9415 22.7696C22.1403 22.7696 22.3035 22.6064 22.3035 22.4076V13.8792L24.8831 16.4637C25.0257 16.6014 25.2554 16.6014 25.3928 16.4637C25.5354 16.3211 25.5354 16.0966 25.3928 15.954L22.1965 12.7576C22.0587 12.6199 21.8294 12.6199 21.6919 12.7576L18.4956 15.954Z"
            fill="#1BA4F7"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M10.4956 19.4699C10.353 19.3273 10.353 19.1028 10.4956 18.9602C10.6334 18.8224 10.8627 18.8224 11.0002 18.9602L13.5847 21.5447V13.0162C13.5847 12.8174 13.7427 12.6542 13.9415 12.6542C14.1403 12.6542 14.3035 12.8174 14.3035 13.0162V21.5447L16.8831 18.9602C17.0257 18.8224 17.2554 18.8224 17.3928 18.9602C17.5354 19.1028 17.5354 19.3273 17.3928 19.4699L14.1965 22.6662C14.0587 22.804 13.8294 22.804 13.6919 22.6662L10.4956 19.4699Z"
            fill="#1BA4F7"
          />
        </svg>
      </div>
    </div>
  </div>

  <!-- To Section -->
  <div
    class="cursor-pointer w-full flex items-center justify-between p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm text-left px-4 mt-6"
    on:click={() => handleClick("To")}
  >
    <div class="flex items-center">
      <div class="flex-shrink-0 mr-3">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="25"
          viewBox="0 0 24 25"
          fill="none"
        >
          <path
            d="M2 22.5H22"
            stroke="#8A8A8A"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M3.77 11.27L2 9.5L4 5L5.1 5.55C5.65 5.83 6 6.39 6 7C6 7.61 6.35 8.17 6.9 8.45L8 9L11 3L12.05 3.53C12.3418 3.67534 12.5937 3.88981 12.7836 4.15473C12.9736 4.41965 13.096 4.72699 13.14 5.05L13.86 10.45C13.904 10.773 14.0264 11.0804 14.2164 11.3453C14.4063 11.6102 14.6582 11.8247 14.95 11.97L19.35 14.17C19.77 14.39 20.13 14.72 20.36 15.13L20.96 16.16C21.45 17.04 20.9 18.14 19.9 18.26L18.72 18.41C18.25 18.47 17.77 18.39 17.35 18.17L4.29 11.65C4.09728 11.5523 3.92167 11.4239 3.77 11.27Z"
            stroke="#8A8A8A"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <div class="flex-1">
        <div class="select-none text-gray-500 pl-3 text-xs">To</div>
        <div class="flex items-baseline">
          <span class="select-none text-gray-900 pl-3 font-bold text-lg"
            >{selectedCities.to.city}</span
          >
          <span class="select-none pl-2 text-sm text-gray-600"
            >{selectedCities.to.code}</span
          >
        </div>
        <div class="select-none text-gray-500 pl-3 text-xs">
          {selectedCities.to.airport}
        </div>
      </div>
    </div>
  </div>
</div>
